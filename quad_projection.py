import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.patches import Polygon
from mpl_toolkits.mplot3d.art3d import Poly3DCollection

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei',
                                  'STHeiti', 'Heiti TC', 'Hiragino Sans GB',
                                  'WenQuanYi Zen Hei', 'WenQuanYi Micro Hei',
                                  'Droid Sans Fallback']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def project_point(point, camera_pos):
    """Project a 3D point onto a 2D plane (z=0)"""
    t = -camera_pos[2] / (point[2] - camera_pos[2])
    return camera_pos + t * (point - camera_pos)

# Create two separate figures
# 3D figure
fig1 = plt.figure(figsize=(10, 8))
ax1 = fig1.add_subplot(111, projection='3d')

# 2D figure
fig2 = plt.figure(figsize=(10, 8))
ax2 = fig2.add_subplot(111)

# Define camera position
camera_pos = np.array([0, 0, -5])

# Define base rectangle in XOY plane (z=0) with 16:9 aspect ratio
# Width = 3.2, Height = 1.8 (16:9 ratio)
base_quad_template = np.array([
    [-1.6, -0.9, 0],  # Bottom-left
    [1.6, -0.9, 0],   # Bottom-right
    [1.6, 0.9, 0],    # Top-right
    [-1.6, 0.9, 0]    # Top-left
])

class QuadConfig:
    """Configuration class for quad transformation parameters"""
    def __init__(self, scale_factor=1.0, translation=(0, 0, 3.5),
                 rotation_x=0, rotation_y=0, rotation_z=0, name="Default"):
        self.scale_factor = scale_factor
        self.translation = np.array(translation)
        self.rotation_x = rotation_x
        self.rotation_y = rotation_y
        self.rotation_z = rotation_z
        self.name = name

    def __str__(self):
        return (f"QuadConfig '{self.name}': scale={self.scale_factor}, "
                f"trans={self.translation}, rot=({self.rotation_x}, {self.rotation_y}, {self.rotation_z})")

# Define different quad configurations
configs = {
    "default": QuadConfig(
        scale_factor=1.0,
        translation=(0, 0, 3.5),
        rotation_x=0, rotation_y=10, rotation_z=0,
        name="Default"
    ),
    "large_tilted": QuadConfig(
        scale_factor=1.5,
        translation=(1, 0.5, 4),
        rotation_x=15, rotation_y=20, rotation_z=10,
        name="Large Tilted"
    ),
    "small_close": QuadConfig(
        scale_factor=0.8,
        translation=(0, 0, 2.5),
        rotation_x=0, rotation_y=0, rotation_z=0,
        name="Small Close"
    ),
    "far_rotated": QuadConfig(
        scale_factor=1.2,
        translation=(-0.5, 1, 5),
        rotation_x=30, rotation_y=-15, rotation_z=25,
        name="Far Rotated"
    ),
    "current": QuadConfig(
        scale_factor=4.0,
        translation=(0, -3, 3.5),
        rotation_x=55, rotation_y=50, rotation_z=-20,
        name="Current Settings"
    )
}

# Select which configuration to use
current_config_name = "current"  # Change this to switch between configurations
config = configs[current_config_name]

print(f"Using configuration: {config}")

# Apply configuration parameters
scale_factor = config.scale_factor
translation = config.translation
rotation_x = config.rotation_x
rotation_y = config.rotation_y
rotation_z = config.rotation_z

# Apply scale factor to base quad
base_quad = base_quad_template * scale_factor

def apply_transformation(points, translation, rotation_x, rotation_y, rotation_z):
    """Apply rotation and translation transformations to points"""
    # Convert angles to radians
    rx = np.radians(rotation_x)
    ry = np.radians(rotation_y)
    rz = np.radians(rotation_z)

    # Rotation matrices
    Rx = np.array([[1, 0, 0],
                   [0, np.cos(rx), -np.sin(rx)],
                   [0, np.sin(rx), np.cos(rx)]])

    Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                   [0, 1, 0],
                   [-np.sin(ry), 0, np.cos(ry)]])

    Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                   [np.sin(rz), np.cos(rz), 0],
                   [0, 0, 1]])

    # Combined rotation matrix (order: Rz * Ry * Rx)
    R = Rz @ Ry @ Rx

    # Apply rotation then translation
    transformed_points = (R @ points.T).T + translation

    return transformed_points

# Apply transformation to get final quad vertices
quad = apply_transformation(base_quad, translation, rotation_x, rotation_y, rotation_z)

# Define physical screen dimensions in pixels
screen_width_px = 1920
screen_height_px = 1080

# Define conversion function from original coordinates to pixel coordinates
def to_pixel_coords(points, screen_width_px, screen_height_px):
    # Define coordinate bounds to match 16:9 aspect ratio of physical screen
    # This ensures proper mapping between coordinate system and pixel dimensions
    aspect_ratio = screen_width_px / screen_height_px  # 16:9 ≈ 1.778

    # Use height of 8 units, calculate width to maintain aspect ratio
    coord_height = 8
    coord_width = coord_height * aspect_ratio

    base_x_min, base_x_max = -coord_width/2, coord_width/2
    base_y_min, base_y_max = -coord_height/2, coord_height/2

    # Convert to [0, 1] range using base coordinates
    normalized_x = (points[:, 0] - base_x_min) / (base_x_max - base_x_min)
    normalized_y = (points[:, 1] - base_y_min) / (base_y_max - base_y_min)

    # Scale to pixel dimensions
    pixel_x = normalized_x * screen_width_px
    pixel_y = normalized_y * screen_height_px

    return np.column_stack((pixel_x, pixel_y))

# Project quad vertices onto z=0 plane
projected_points_original = np.array([project_point(p, camera_pos) for p in quad])

# ===== 3D VISUALIZATION (LEFT SUBPLOT) =====
# Draw the 3D quad
quad_3d = np.vstack((quad, quad[0]))  # Close the loop
ax1.plot(quad_3d[:, 0], quad_3d[:, 1], quad_3d[:, 2], 'r-', linewidth=2)

# Label the corners of the virtual screen (3D quad) with A, B, C, D
corner_names_virtual = ["A", "B", "C", "D"]
for i, (point, name) in enumerate(zip(quad, corner_names_virtual)):
    # Add a small offset to make labels more visible
    offset_x = 0.1 if i in [0, 3] else -0.1  # Left side vs right side
    offset_y = 0.1 if i in [2, 3] else -0.1  # Top vs bottom
    offset_z = 0.1  # Slight offset in z direction
    ax1.text(point[0] + offset_x, point[1] + offset_y, point[2] + offset_z,
             name, fontsize=12, color='darkred', fontweight='bold')

# Draw the projected quad in 3D space
proj_quad_3d = np.vstack((projected_points_original, projected_points_original[0]))  # Close the loop
ax1.plot(proj_quad_3d[:, 0], proj_quad_3d[:, 1], proj_quad_3d[:, 2], 'g-', linewidth=2)

# Label the corners of the projected quad with A', B', C', D'
corner_names_projected = ["A'", "B'", "C'", "D'"]
for i, (point, name) in enumerate(zip(projected_points_original, corner_names_projected)):
    # Add a small offset to make labels more visible
    offset_x = 0.1 if i in [0, 3] else -0.1  # Left side vs right side
    offset_y = 0.1 if i in [2, 3] else -0.1  # Top vs bottom
    ax1.text(point[0] + offset_x, point[1] + offset_y, point[2],
             name, fontsize=12, color='green', fontweight='bold')

# Draw projection lines
for p, proj_p in zip(quad, projected_points_original):
    ax1.plot([camera_pos[0], p[0]], [camera_pos[1], p[1]], [camera_pos[2], p[2]], 'k--', linewidth=1)
    ax1.plot([p[0], proj_p[0]], [p[1], proj_p[1]], [p[2], proj_p[2]], 'b:', linewidth=1)

# Draw projection center
ax1.scatter([camera_pos[0]], [camera_pos[1]], [camera_pos[2]], color='blue', s=100)

# Define a scale factor for the projection plane (>1.0 makes it appear larger)
projection_plane_scale = 2.0

# Draw projection plane (z=0) with expanded size based on projection_plane_scale
plane_range = 4 * projection_plane_scale
x = np.linspace(-plane_range, plane_range, 10)
y = np.linspace(-plane_range, plane_range, 10)
X, Y = np.meshgrid(x, y)
Z = np.zeros_like(X)
ax1.plot_surface(X, Y, Z, color='cyan', alpha=0.2, edgecolor='none')

# Draw physical screen outline in 3D space
# First convert pixel coordinates back to 3D space coordinates
def from_pixel_to_3d_coords(pixel_points):
    # Use the same coordinate bounds as in to_pixel_coords to maintain consistency
    aspect_ratio = screen_width_px / screen_height_px  # 16:9 ≈ 1.778

    # Use height of 8 units, calculate width to maintain aspect ratio
    coord_height = 8
    coord_width = coord_height * aspect_ratio

    base_x_min, base_x_max = -coord_width/2, coord_width/2
    base_y_min, base_y_max = -coord_height/2, coord_height/2

    # Convert from pixels to [0, 1] range
    normalized_x = pixel_points[:, 0] / screen_width_px
    normalized_y = pixel_points[:, 1] / screen_height_px

    # Scale to base coordinate range
    orig_x = normalized_x * (base_x_max - base_x_min) + base_x_min
    orig_y = normalized_y * (base_y_max - base_y_min) + base_y_min

    # Z coordinate is 0 for all points (on projection plane)
    orig_z = np.zeros_like(orig_x)

    return np.column_stack((orig_x, orig_y, orig_z))

# Define physical screen corners in pixel coordinates (full size)
screen_corners_px = np.array([
    [0, 0],                           # 左下
    [screen_width_px, 0],             # 右下
    [screen_width_px, screen_height_px], # 右上
    [0, screen_height_px]             # 左上
])

# Keep physical screen at actual size (no scaling)
display_scale = 1.0
display_offset_x = (1 - display_scale) * screen_width_px / 2
display_offset_y = (1 - display_scale) * screen_height_px / 2

# Convert to 3D coordinates
screen_corners_3d = from_pixel_to_3d_coords(screen_corners_px)

# Draw the physical screen outline in 3D with a filled surface
screen_outline_3d = np.vstack((screen_corners_3d, screen_corners_3d[0]))  # Close the loop

# Create a filled polygon for the screen

# Create a smaller display version of the screen for visualization
# Scale the screen corners for display purposes
display_corners_3d = screen_corners_3d.copy()
# Calculate center of the screen
center_x = np.mean(display_corners_3d[:, 0])
center_y = np.mean(display_corners_3d[:, 1])
# Scale around the center
for i in range(len(display_corners_3d)):
    display_corners_3d[i, 0] = center_x + (display_corners_3d[i, 0] - center_x) * display_scale
    display_corners_3d[i, 1] = center_y + (display_corners_3d[i, 1] - center_y) * display_scale

# Create a polygon for the screen (smaller display version)
screen_poly = Poly3DCollection([display_corners_3d], alpha=0.3, linewidth=2,
                              edgecolor='r', facecolor='r', linestyle='--')
ax1.add_collection3d(screen_poly)

# No labels for the real screen corners in 3D view

# Set labels and title for 3D plot
ax1.set_xlabel('X轴')
ax1.set_ylabel('Y轴')
ax1.set_zlabel('Z轴')
ax1.set_title('虚拟屏幕及其投影')

# Create a custom legend without duplicate entries
from matplotlib.lines import Line2D
from matplotlib.patches import Patch

legend_elements = [
    Line2D([0], [0], color='r', linewidth=2, label='虚拟屏幕'),
    Line2D([0], [0], color='g', linewidth=2, label='投影四边形'),
    Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='投影中心'),
    # 使用Patch对象来表示真实屏幕
    Patch(facecolor='r', edgecolor='r', alpha=0.3, label='真实屏幕'),
    # 使用Patch对象来表示投影平面
    Patch(facecolor='cyan', edgecolor='cyan', alpha=0.2, label='投影平面')
]
ax1.legend(handles=legend_elements)

# Set equal aspect ratio for 3D view to maintain consistent unit lengths
ax1.set_box_aspect([1,1,1])  # Equal aspect ratio for x, y, z axes

# Set view angle
ax1.view_init(elev=20, azim=-35)

# ===== 2D VISUALIZATION (RIGHT SUBPLOT) =====
# Convert projected points to pixel coordinates
projected_points = to_pixel_coords(projected_points_original, screen_width_px, screen_height_px)

# Draw only the projected quad
proj_quad = np.vstack((projected_points, projected_points[0]))  # Close the loop
ax2.plot(proj_quad[:, 0], proj_quad[:, 1], 'g-', linewidth=2, label="投影四边形")

# Fill the projected quad
ax2.fill(projected_points[:, 0], projected_points[:, 1], 'g', alpha=0.3)

# Label the edges of the projected quad with l₁, l₂, l₃, l₄ (using LaTeX for subscripts)
edge_names = ["$l_{1}$", "$l_{2}$", "$l_{3}$", "$l_{4}$"]
for i in range(4):
    # Calculate midpoint of each edge
    start_point = projected_points[i]
    end_point = projected_points[(i+1) % 4]  # Wrap around to first point for the last edge
    mid_x = (start_point[0] + end_point[0]) / 2
    mid_y = (start_point[1] + end_point[1]) / 2

    # Calculate perpendicular offset direction for the label
    # Get edge direction vector
    dx = end_point[0] - start_point[0]
    dy = end_point[1] - start_point[1]
    # Perpendicular vector (rotate 90 degrees)
    perp_dx, perp_dy = -dy, dx
    # Normalize and scale for offset
    length = np.sqrt(perp_dx**2 + perp_dy**2)
    if length > 0:
        offset_x = perp_dx / length * 30  # 30 pixels offset
        offset_y = perp_dy / length * 30
    else:
        offset_x, offset_y = 0, 30  # Default offset if edge length is zero

    # Add the label with LaTeX formatting for subscripts
    ax2.text(mid_x + offset_x, mid_y + offset_y, edge_names[i], fontsize=10,
            ha='center', va='center', color='green', fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="green", alpha=0.8))

# Label the corners of the projected quad with A', B', C', D' and coordinates
# Use the same order as in the 3D visualization
corner_names = ["A'", "B'", "C'", "D'"]
for i, (point, name) in enumerate(zip(projected_points, corner_names)):
    # Round coordinates to integers for cleaner display
    x, y = int(point[0]), int(point[1])
    label = f"{name} ({x}, {y})"
    # Position the label with a small offset from the corner
    offset_x = 20 if i in [0, 3] else -20  # Left side vs right side
    offset_y = 20 if i in [2, 3] else -20  # Top vs bottom
    ax2.text(point[0] + offset_x, point[1] + offset_y, label, fontsize=9,
            ha='center', va='center', backgroundcolor='white', alpha=0.8,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="green", alpha=0.8))

# Set labels and title
ax2.set_xlabel('X轴 (像素)')
ax2.set_ylabel('Y轴 (像素)')
ax2.set_title('二维平面上的投影结果 (像素坐标)')

# Add a smaller rectangle representing the physical screen for display purposes
# Use the same scaling factor as in 3D view
rect = plt.Rectangle((display_offset_x, display_offset_y),
                     screen_width_px * display_scale, screen_height_px * display_scale,
                     linewidth=2, edgecolor='r', facecolor='r',
                     linestyle='--', alpha=0.3, label='真实屏幕')
ax2.add_patch(rect)

# Label the corners of the physical screen with their true coordinates
screen_corners = [
    (0, 0),                           # 左下
    (screen_width_px, 0),             # 右下
    (screen_width_px, screen_height_px), # 右上
    (0, screen_height_px)             # 左上
]
# Use the true coordinates for labels
screen_corner_labels = [
    '(0, 0)',
    f'({screen_width_px}, 0)',
    f'({screen_width_px}, {screen_height_px})',
    f'(0, {screen_height_px})'
]

# But position the labels near the displayed smaller rectangle
display_corners = [
    (display_offset_x, display_offset_y),                                   # 左下
    (display_offset_x + screen_width_px * display_scale, display_offset_y), # 右下
    (display_offset_x + screen_width_px * display_scale, display_offset_y + screen_height_px * display_scale), # 右上
    (display_offset_x, display_offset_y + screen_height_px * display_scale) # 左上
]

# Place the labels near the displayed smaller rectangle corners
for i, (display_pos, label) in enumerate(zip(display_corners, screen_corner_labels)):
    # 调整标签位置，使其不会与角落重叠
    offset_x = 15 if i in [0, 3] else -15  # 左侧 vs 右侧
    offset_y = 15 if i in [2, 3] else -15  # 上方 vs 下方
    ax2.text(display_pos[0] + offset_x, display_pos[1] + offset_y, label, fontsize=9,
            ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="red", alpha=0.8))

# Set equal aspect ratio to maintain consistent unit lengths
ax2.set_aspect('equal')

# Calculate dynamic axis limits to show both physical screen and projected quad
# Get the bounds of the projected quad
proj_x_min = np.min(projected_points[:, 0])
proj_x_max = np.max(projected_points[:, 0])
proj_y_min = np.min(projected_points[:, 1])
proj_y_max = np.max(projected_points[:, 1])

# Physical screen bounds
screen_x_min, screen_x_max = 0, screen_width_px
screen_y_min, screen_y_max = 0, screen_height_px

# Calculate overall bounds including both screen and projection
overall_x_min = min(proj_x_min, screen_x_min)
overall_x_max = max(proj_x_max, screen_x_max)
overall_y_min = min(proj_y_min, screen_y_min)
overall_y_max = max(proj_y_max, screen_y_max)

# Add margins (20% of the range on each side)
x_range = overall_x_max - overall_x_min
y_range = overall_y_max - overall_y_min
margin_x = max(x_range * 0.2, 100)  # At least 100 pixels margin
margin_y = max(y_range * 0.2, 100)  # At least 100 pixels margin

# Set axis limits with margins
ax2.set_xlim(overall_x_min - margin_x, overall_x_max + margin_x)
ax2.set_ylim(overall_y_min - margin_y, overall_y_max + margin_y)

# Add grid
ax2.grid(True)

# Move the axes to the figure edges
ax2.spines['left'].set_position(('data', 0))
ax2.spines['bottom'].set_position(('data', 0))
ax2.spines['top'].set_visible(False)
ax2.spines['right'].set_visible(False)

# Adjust tick positions
ax2.xaxis.set_ticks_position('bottom')
ax2.yaxis.set_ticks_position('left')

# Add more ticks for better reference
ax2.set_xticks(np.arange(0, screen_width_px + 1, 200))
ax2.set_yticks(np.arange(0, screen_height_px + 1, 200))

# Remove numerical labels along the axes but keep the ticks and grid
ax2.set_xticklabels([])
ax2.set_yticklabels([])

# Add legend after all elements are added
ax2.legend(loc='upper right')

# Adjust layout and show figures
fig1.tight_layout()
fig1.canvas.manager.set_window_title('3D视图 - 虚拟屏幕及其投影')

fig2.tight_layout()
fig2.canvas.manager.set_window_title('2D视图 - 投影结果')

plt.show()