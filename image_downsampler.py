#!/usr/bin/env python3
"""
图片降采样工具
支持按任意水平和垂直方向压缩比对图片进行降采样
"""

import cv2
import numpy as np
import argparse
import os
from pathlib import Path


def downsample_image(image, horizontal_ratio, vertical_ratio, interpolation=cv2.INTER_LINEAR):
    """
    对图片进行降采样
    
    Args:
        image: 输入图片 (numpy array)
        horizontal_ratio: 水平方向压缩比 (0 < ratio <= 1.0)
        vertical_ratio: 垂直方向压缩比 (0 < ratio <= 1.0)
        interpolation: 插值方法
        
    Returns:
        降采样后的图片 (numpy array)
    """
    if horizontal_ratio <= 0 or horizontal_ratio > 1.0:
        raise ValueError("水平压缩比必须在 (0, 1] 范围内")
    if vertical_ratio <= 0 or vertical_ratio > 1.0:
        raise ValueError("垂直压缩比必须在 (0, 1] 范围内")
    
    height, width = image.shape[:2]
    
    # 计算新的尺寸
    new_width = int(width * horizontal_ratio)
    new_height = int(height * vertical_ratio)
    
    # 确保新尺寸至少为1像素
    new_width = max(1, new_width)
    new_height = max(1, new_height)
    
    # 进行降采样
    downsampled = cv2.resize(image, (new_width, new_height), interpolation=interpolation)
    
    return downsampled


def get_interpolation_method(method_name):
    """获取插值方法"""
    methods = {
        'linear': cv2.INTER_LINEAR,
        'nearest': cv2.INTER_NEAREST,
        'cubic': cv2.INTER_CUBIC,
        'area': cv2.INTER_AREA,
        'lanczos4': cv2.INTER_LANCZOS4
    }
    return methods.get(method_name.lower(), cv2.INTER_LINEAR)


def generate_output_filename(input_path, horizontal_ratio, vertical_ratio, suffix=""):
    """生成输出文件名"""
    input_path = Path(input_path)
    stem = input_path.stem
    ext = input_path.suffix
    
    # 创建描述性的文件名
    h_ratio_str = f"{horizontal_ratio:.3f}".rstrip('0').rstrip('.')
    v_ratio_str = f"{vertical_ratio:.3f}".rstrip('0').rstrip('.')
    
    if suffix:
        output_name = f"{stem}_h{h_ratio_str}_v{v_ratio_str}_{suffix}{ext}"
    else:
        output_name = f"{stem}_h{h_ratio_str}_v{v_ratio_str}{ext}"
    
    return input_path.parent / output_name


def main():
    parser = argparse.ArgumentParser(description="图片降采样工具")
    parser.add_argument("input", help="输入图片路径")
    parser.add_argument("-hr", "--horizontal-ratio", type=float, required=True,
                       help="水平方向压缩比 (0 < ratio <= 1.0)")
    parser.add_argument("-vr", "--vertical-ratio", type=float, required=True,
                       help="垂直方向压缩比 (0 < ratio <= 1.0)")
    parser.add_argument("-o", "--output", help="输出图片路径 (可选，默认自动生成)")
    parser.add_argument("-m", "--method", default="linear",
                       choices=['linear', 'nearest', 'cubic', 'area', 'lanczos4'],
                       help="插值方法 (默认: linear)")
    parser.add_argument("--preview", action="store_true",
                       help="显示原图和降采样后的对比预览")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"错误: 输入文件 '{args.input}' 不存在")
        return 1
    
    try:
        # 读取图片
        print(f"读取图片: {args.input}")
        image = cv2.imread(args.input, cv2.IMREAD_COLOR)
        if image is None:
            print(f"错误: 无法读取图片 '{args.input}'")
            return 1
        
        original_height, original_width = image.shape[:2]
        print(f"原始尺寸: {original_width} x {original_height}")
        
        # 获取插值方法
        interpolation = get_interpolation_method(args.method)
        
        # 进行降采样
        print(f"应用压缩比: 水平 {args.horizontal_ratio}, 垂直 {args.vertical_ratio}")
        downsampled = downsample_image(image, args.horizontal_ratio, args.vertical_ratio, interpolation)
        
        new_height, new_width = downsampled.shape[:2]
        print(f"新尺寸: {new_width} x {new_height}")
        
        # 生成输出文件名
        if args.output:
            output_path = args.output
        else:
            output_path = generate_output_filename(args.input, args.horizontal_ratio, 
                                                 args.vertical_ratio, args.method)
        
        # 保存图片
        print(f"保存到: {output_path}")
        success = cv2.imwrite(str(output_path), downsampled)
        if not success:
            print(f"错误: 无法保存图片到 '{output_path}'")
            return 1
        
        print("降采样完成!")
        
        # 显示预览
        if args.preview:
            print("显示预览窗口 (按任意键关闭)")

            # 保持真实比例显示，如果图片太大则整体缩放
            max_display_width = 1200
            max_display_height = 800

            # 计算是否需要缩放以适应显示
            scale_factor = 1.0
            if original_width > max_display_width or original_height > max_display_height:
                scale_w = max_display_width / original_width
                scale_h = max_display_height / original_height
                scale_factor = min(scale_w, scale_h)

            # 按比例缩放原图用于显示
            display_orig_w = int(original_width * scale_factor)
            display_orig_h = int(original_height * scale_factor)
            display_original = cv2.resize(image, (display_orig_w, display_orig_h))

            # 降采样图片保持真实的相对尺寸
            display_down_w = int(new_width * scale_factor)
            display_down_h = int(new_height * scale_factor)
            display_downsampled = cv2.resize(downsampled, (display_down_w, display_down_h))

            # 在图片上添加标签
            label_height = 30

            # 为原图添加标签
            labeled_original = np.zeros((display_orig_h + label_height, display_orig_w, 3), dtype=np.uint8)
            labeled_original[:label_height] = (50, 50, 50)  # 深灰色背景
            labeled_original[label_height:] = display_original
            cv2.putText(labeled_original, f"原图: {original_width}x{original_height}",
                       (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

            # 为降采样图片添加标签
            labeled_downsampled = np.zeros((display_down_h + label_height, display_down_w, 3), dtype=np.uint8)
            labeled_downsampled[:label_height] = (50, 50, 50)  # 深灰色背景
            labeled_downsampled[label_height:] = display_downsampled
            cv2.putText(labeled_downsampled, f"降采样: {new_width}x{new_height} (h:{args.horizontal_ratio:.2f}, v:{args.vertical_ratio:.2f})",
                       (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

            # 计算合并图片的尺寸
            combined_width = max(labeled_original.shape[1], labeled_downsampled.shape[1])
            combined_height = labeled_original.shape[0] + labeled_downsampled.shape[0] + 20  # 20像素间隔

            combined_image = np.zeros((combined_height, combined_width, 3), dtype=np.uint8)
            combined_image[:] = (30, 30, 30)  # 深灰色背景

            # 放置原图（左对齐，便于比较尺寸）
            combined_image[:labeled_original.shape[0], :labeled_original.shape[1]] = labeled_original

            # 放置降采样图片（左对齐，在原图下方）
            down_y_offset = labeled_original.shape[0] + 20
            combined_image[down_y_offset:down_y_offset + labeled_downsampled.shape[0],
                          :labeled_downsampled.shape[1]] = labeled_downsampled

            # 添加比例信息
            info_text = f"Scale factor for display: {scale_factor:.2f}x"
            cv2.putText(combined_image, info_text, (10, combined_height - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

            # 显示合并后的图片
            cv2.imshow('Image Downsampling Comparison (True Scale)', combined_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        
        return 0
        
    except ValueError as e:
        print(f"参数错误: {e}")
        return 1
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
