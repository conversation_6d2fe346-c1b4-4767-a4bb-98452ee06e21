# 图片降采样工具

这个Python脚本可以对输入图片按任意水平和垂直方向的压缩比进行降采样。

## 功能特点

- ✅ 支持任意水平和垂直压缩比
- ✅ 多种插值方法选择
- ✅ 自动生成描述性输出文件名
- ✅ 可选的预览功能
- ✅ 支持常见图片格式

## 依赖库

```bash
pip install opencv-python numpy
```

## 使用方法

### 基本用法

```bash
python image_downsampler.py input.jpg -hr 0.5 -vr 0.5
```

### 参数说明

- `input`: 输入图片路径
- `-hr, --horizontal-ratio`: 水平方向压缩比 (0 < ratio ≤ 1.0)
- `-vr, --vertical-ratio`: 垂直方向压缩比 (0 < ratio ≤ 1.0)
- `-o, --output`: 输出图片路径 (可选，默认自动生成)
- `-m, --method`: 插值方法 (默认: linear)
- `--preview`: 显示原图和降采样后的对比预览

### 插值方法

- `linear`: 双线性插值 (默认)
- `nearest`: 最近邻插值
- `cubic`: 双三次插值
- `area`: 区域插值 (适合缩小)
- `lanczos4`: Lanczos插值

## 使用示例

### 1. 基本降采样
```bash
# 水平和垂直都压缩到原来的50%
python image_downsampler.py photo.jpg -hr 0.5 -vr 0.5
```

### 2. 不同方向不同压缩比
```bash
# 水平压缩到70%，垂直压缩到30%
python image_downsampler.py photo.jpg -hr 0.7 -vr 0.3
```

### 3. 指定输出文件和插值方法
```bash
# 使用cubic插值，指定输出文件名
python image_downsampler.py input.png -hr 0.8 -vr 0.6 -m cubic -o output.png
```

### 4. 带预览功能
```bash
# 显示对比预览窗口
python image_downsampler.py photo.jpg -hr 0.4 -vr 0.4 --preview
```

## 输出文件命名规则

如果不指定输出文件名，会自动生成描述性文件名：

- 原文件: `photo.jpg`
- 压缩比: 水平0.5，垂直0.7，方法linear
- 输出: `photo_h0.5_v0.7_linear.jpg`

## 注意事项

1. **压缩比范围**: 必须在 (0, 1] 范围内
2. **最小尺寸**: 降采样后的图片尺寸至少为1x1像素
3. **文件格式**: 支持OpenCV支持的所有图片格式
4. **内存使用**: 大图片处理时注意内存使用

## 实际应用场景

### 1. 数据集预处理
```bash
# 为机器学习准备小尺寸图片
python image_downsampler.py dataset/image.jpg -hr 0.25 -vr 0.25 -m area
```

### 2. 网页优化
```bash
# 生成缩略图
python image_downsampler.py banner.png -hr 0.3 -vr 0.3 -o thumbnail.png
```

### 3. 存储空间优化
```bash
# 批量处理（配合shell脚本）
for img in *.jpg; do
    python image_downsampler.py "$img" -hr 0.6 -vr 0.6 -m area
done
```

### 4. 特殊宽高比调整
```bash
# 创建宽屏效果（保持宽度，压缩高度）
python image_downsampler.py photo.jpg -hr 1.0 -vr 0.4
```

## 错误处理

脚本包含完整的错误处理：
- 文件不存在检查
- 参数范围验证
- 图片读取失败处理
- 保存失败处理
