import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import art3d  # For 3D patch
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
from matplotlib.patches import Rectangle
from shapely.geometry import Polygon as ShapelyPolygon

# 允许 matplotlib 显示中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def project_point(COP, point):
    """ 计算 3D 点在 z=0 平面的透视投影 """
    t = (0 - COP[2]) / (point[2] - COP[2])
    return COP + t * (point - COP)

def generate_projection_scenario(ax, COP, virtual_screen, real_screen):
    """ 绘制投影示意图，包括投影平面、虚拟屏幕、投影矩形和真实屏幕 """
    
    # 计算投影到 z=0 平面的 4 个角点
    projected_points = np.array([project_point(COP, p) for p in virtual_screen])

    # 绘制投影的四边形
    proj_quad = np.vstack((projected_points, projected_points[0]))
    ax.plot(proj_quad[:, 0], proj_quad[:, 1], proj_quad[:, 2], 'g-', linewidth=2, label="投影 (A', B', C', D')")

    # **绘制投影平面（z=0），让它可视化**
    plane_x = np.linspace(-3, 3, 10)
    plane_y = np.linspace(-3, 3, 10)
    X, Y = np.meshgrid(plane_x, plane_y)
    Z = np.zeros_like(X)  # 投影平面的 z 坐标全为 0
    ax.plot_surface(X, Y, Z, color='cyan', alpha=0.3, edgecolor='none', label="投影平面")  # 半透明平面

    # **绘制真实屏幕（ROI）**
    screen_patch = Rectangle((real_screen[0, 0], real_screen[0, 1]), 
                             real_screen[1, 0] - real_screen[0, 0], 
                             real_screen[2, 1] - real_screen[1, 1], 
                             color='orange', alpha=0.7, label="真实屏幕")
    ax.add_patch(screen_patch)
    ax.text(real_screen[0, 0], real_screen[0, 1], 0.05, "真实屏幕", color='black')

    # **将真实屏幕贴到 z=0 平面**
    art3d.pathpatch_2d_to_3d(screen_patch, z=0, zdir="z")

    # **绘制投影光线**
    for p, proj_p in zip(virtual_screen, projected_points):
        ax.plot([COP[0], p[0]], [COP[1], p[1]], [COP[2], p[2]], 'k--', linewidth=1)
        ax.plot([p[0], proj_p[0]], [p[1], proj_p[1]], [p[2], proj_p[2]], 'm:', linewidth=1)

    # 绘制虚拟屏幕（3D 空间）
    quad = np.vstack((virtual_screen, virtual_screen[0]))  # 连接首尾形成封闭矩形
    ax.plot(quad[:, 0], quad[:, 1], quad[:, 2], 'r-', linewidth=2, label="虚拟屏幕")

    # **标注虚拟屏幕四个角（A, B, C, D）**
    ax.text(virtual_screen[0, 0], virtual_screen[0, 1], virtual_screen[0, 2]+ 0.2, "A", color='red')
    ax.text(virtual_screen[1, 0], virtual_screen[1, 1], virtual_screen[1, 2]+ 0.2, "B", color='red')
    ax.text(virtual_screen[2, 0], virtual_screen[2, 1], virtual_screen[2, 2]+ 0.2, "C", color='red')
    ax.text(virtual_screen[3, 0], virtual_screen[3, 1], virtual_screen[3, 2]+ 0.2, "D", color='red')

    # **标注投影后的四个点（A', B', C', D'）**
    ax.text(projected_points[0, 0], projected_points[0, 1], projected_points[0, 2] + 0.2, "A'", color='green')
    ax.text(projected_points[1, 0], projected_points[1, 1], projected_points[1, 2] + 0.2, "B'", color='green')
    ax.text(projected_points[2, 0], projected_points[2, 1], projected_points[2, 2] + 0.2, "C'", color='green')
    ax.text(projected_points[3, 0], projected_points[3, 1], projected_points[3, 2] + 0.2, "D'", color='green')

    # **计算投影与真实屏幕的重叠情况**
    proj_poly_2d = [(p[0], p[1]) for p in projected_points]
    screen_poly_2d = [(p[0], p[1]) for p in real_screen]
    shapely_proj = ShapelyPolygon(proj_poly_2d)
    shapely_screen = ShapelyPolygon(screen_poly_2d)
    overlap = shapely_proj.intersects(shapely_screen)
    
    # **显示是否重叠**
    #overlap_text = "投影与真实屏幕：重叠" if overlap else "投影与真实屏幕：无重叠"
    #ax.text2D(0.05, 0.95, overlap_text, transform=ax.transAxes, fontsize=14, color='purple')

    # **设置坐标轴**
    ax.set_xlabel("X轴")
    ax.set_ylabel("Y轴")
    ax.set_zlabel("Z轴")
    ax.legend()

    # 设置视角
    ax.view_init(elev=30, azim=-30)

# **创建单个投影场景**
fig = plt.figure(figsize=(8, 6))
ax = fig.add_subplot(111, projection='3d')

# **手动设定一个场景，确保虚拟屏幕是一个矩形**
COP = np.array([0, 0, -5])  # 投影中心
virtual_screen = np.array([[-1.6, -0.9, 2], [1.6, -0.9, 2], [1.6, 0.9, 3], [-1.6, 0.9, 3]])  # 3D 虚拟屏幕的四个角点

# **真实屏幕（ROI），固定在 z=0**
real_screen = np.array([[-1.8, -1.8, 0], [1.8, -1.8, 0], [1.8, 1.8, 0], [-1.8, 1.8, 0]])

# **生成并绘制单个场景**
generate_projection_scenario(ax, COP, virtual_screen, real_screen)

plt.show()
